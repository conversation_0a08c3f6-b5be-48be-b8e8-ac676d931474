{"test_date": "2025-04-09", "timestamp": "20250702_214903", "results": {"my.app.tngd.h3ccoreswitch": {"success": true, "row_count": 6, "data": [{"eventdate": 1744157161750, "cluster": "-", "instance": "-", "message": "TNG-Controller-2 %%10APMGR/6/APMGR_LOG_CHANNELCHANGE: Channel of Radio 1 on AP Level_3_AP04 changed from 52 to 36. Reason: Avoid radar channel."}, {"eventdate": 1744157161752, "cluster": "-", "instance": "-", "message": "TNG-Controller-2 %%10APMGR/6/APMGR_LOG_CHANNELCHANGE: Channel of Radio 1 on AP Level_3_AP04 changed from 52 to 36. Reason: Avoid radar channel."}]}, "my.app.tngd.keeper": {"success": true, "row_count": 8, "data": [{"eventdate": 1744246045434, "cluster": "-", "instance": "-", "message": "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)"}, {"eventdate": 1744246045437, "cluster": "-", "instance": "-", "message": "GET / HTTP/1.1"}]}, "my.app.tngd.h3cswitch": {"success": true, "row_count": 5, "data": [{"eventdate": 1744160404607, "cluster": "-", "instance": "-", "message": "ACS-TNGD-L10-1 %%10SHELL/6/SHELL_CMD: -Line=-IPAddr=**-User=**; Command is system-view"}, {"eventdate": 1744160405742, "cluster": "-", "instance": "-", "message": "ACS-TNGD-L10-1 %%10SHELL/6/SHELL_CMD: -Line=-IPAddr=**-User=**; Command is interface range GigabitEthernet 2/0/17"}]}, "my.app.tngd.waf": {"success": true, "row_count": 1, "data": [{"eventdate": 1744156801017, "cluster": "-", "instance": "-", "messages": "{\"time\":\"2025-03-05T00:39:57+08:00\",\"ssl_protocol\":\"TLSv1.3\",\"http_x_forwarded_for\":\"-\",\"body_bytes_sent\":\"0\",\"request_time_msec\":\"27\",\"request_method\":\"POST\",\"dst_port\":\"443\",\"https\":\"on\",\"upstream_response_time\":\"0.025\",\"host\":\"mdap.tngdigital.com.my\",\"ssl_cipher\":\"TLS_AES_256_GCM_SHA384\",\"real_client_ip\":\"**************\",\"request_traceid\":\"ac11000117411063974527680e006c\",\"src_ip\":\"**************\",\"region\":\"int\",\"request_uri\":\"/loggw/webLog.do\",\"src_port\":\"45396\",\"status\":\"499\",\"matched_host\":\"mdap.tngdigital.com.my-waf\",\"http_referer\":\"https://cdn.tngdigital.com.my/\",\"remote_addr\":\"**************\",\"http_cookie\":\"-\",\"__topic__\":\"waf_access_log\",\"request_path\":\"/loggw/webLog.do\",\"querystring\":\"-\",\"__source__\":\"xlogc\",\"remote_port\":\"45396\",\"start_time\":\"1741106397\",\"user_id\":\"5478802082436037\",\"http_user_agent\":\"Mozilla/5.0 (Linux; Android 14; SM-S908E Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/133.0.6943.122 Mobile Safari/537.36 Language/en Ariver/1.0.0 Griver/2.68.0 AppContainer/10.5.10 /TNGKit/1.8.49 Touch 'n Go/1.8.49AlipayConnect /TNGKit/1.8.49 Touch 'n Go/1.8.49AlipayConnect  iapconnectsdk/2.49.0\",\"server_protocol\":\"HTTP/1.1\",\"content_type\":\"application/x-www-form-urlencoded\",\"request_length\":\"1790\",\"upstream_status\":\"-\",\"__time__\":\"1741106397\",\"upstream_addr\":\"*************:443\"}", "messages___time__": "1741106397", "messages___topic__": "waf_access_log", "messages___source__": "xlogc", "messages_http_referer": "https://cdn.tngdigital.com.my/", "messages_http_cookie": "-", "messages_ssl_protocol": "TLSv1.3", "messages_src_ip": 2944948332, "messages_server_protocol": "HTTP/1.1", "messages_src_port": "45396", "messages_remote_port": "45396", "messages_remote_addr": 2944948332, "messages_http_x_forwarded_for": "-", "messages_start_time": "1741106397", "messages_upstream_status": "-", "messages_region": "int", "messages_upstream_response_time": "0.025", "messages_querystring": "-", "messages_body_bytes_sent": "0", "messages_request_method": "POST", "messages_request_uri": "/loggw/webLog.do", "messages_real_client_ip": 2944948332, "messages_matched_host": "mdap.tngdigital.com.my-waf", "messages_upstream_addr": "*************:443", "messages_content_type": "application/x-www-form-urlencoded", "messages_dst_port": "443", "messages_status": "499", "messages_request_traceid": "ac11000117411063974527680e006c", "messages_request_length": "1790", "messages_time": "2025-03-05T00:39:57+08:00", "messages_https": "on", "messages_request_path": "/loggw/webLog.do", "messages_request_time_msec": "27", "messages_host": "mdap.tngdigital.com.my", "messages_http_user_agent": "Mozilla/5.0 (Linux; Android 14; SM-S908E Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/133.0.6943.122 Mobile Safari/537.36 Language/en Ariver/1.0.0 Griver/2.68.0 AppContainer/10.5.10 /TNGKit/1.8.49 Touch 'n Go/1.8.49AlipayConnect /TNGKit/1.8.49 Touch 'n Go/1.8.49AlipayConnect  iapconnectsdk/2.49.0", "messages_user_id": "5478802082436037", "messages_ssl_cipher": "TLS_AES_256_GCM_SHA384", "messages_final_plugin": null, "messages_final_rule_id": null, "messages_final_action": null, "messages_bypass_matched_ids": null, "messages_request_traceid_origin": null}]}, "my.app.tngd.actiontraillinux": {"success": true, "row_count": 1, "data": [{"eventdate": 1744156802792, "cluster": "-", "instance": "-", "messages": "{\"__tag__:__path__\":\"/var/log/secure\",\"__tag__:__pack_id__\":\"F6F66EE1246D923-6247\",\"content\":\"2025-04-09T08:00:09+08:00 ************* iZ8ps47wx4fe2e953ohps7Z sshd[27718]: Did not receive identification string from ************* port 23018\",\"__topic__\":\"\",\"__tag__:__hostname__\":\"security-syslog-1-3\",\"__time__\":\"1744156799\",\"__source__\":\"************\"}", "messages___time__": "1744156799", "messages___topic__": "", "messages___source__": 184290841, "messages___tag__:__hostname__": "security-syslog-1-3", "messages___tag__:__path__": "/var/log/secure", "messages___tag__:__pack_id__": "F6F66EE1246D923-6247", "messages_content": "2025-04-09T08:00:09+08:00 ************* iZ8ps47wx4fe2e953ohps7Z sshd[27718]: Did not receive identification string from ************* port 23018"}]}}}