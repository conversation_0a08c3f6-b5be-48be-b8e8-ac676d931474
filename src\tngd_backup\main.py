#!/usr/bin/env python3
"""
TNGD Backup System - Main Entry Point

Professional backup system with advanced features:
- Resource management and optimization
- Real-time monitoring and progress tracking
- Automatic recovery and checkpoint system
- Comprehensive error handling and retry logic
- OSS storage integration with compression

Usage:
    python -m tngd_backup.main                    # Today's data
    python -m tngd_backup.main 2025-03-26         # Single date
    python -m tngd_backup.main 2025-03-26 2025-03-31  # Date range
"""

import sys
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tngd_backup.core.backup_engine import BackupEngine
from tngd_backup.utils.monitoring import SystemHealthChecker


def parse_dates(date_args: List[str]) -> List[datetime]:
    """
    Parse command line date arguments.

    NOTE: Historical backup functionality removed.
    This function now always returns current timestamp for backup identification only.

    Args:
        date_args: List of date strings from command line (ignored)

    Returns:
        List containing single datetime object for current backup run
    """
    # Always return current timestamp - no historical backup support
    return [datetime.now()]


def check_system_readiness() -> bool:
    """
    Check if system is ready for backup operations.
    
    Returns:
        bool: True if system is ready
    """
    health_checker = SystemHealthChecker()
    health_check = health_checker.check_backup_readiness()
    
    if health_check["errors"]:
        print("ERROR: System readiness check failed:")
        for error in health_check["errors"]:
            print(f"   - {error}")
        return False

    if health_check["warnings"]:
        print("WARNING: System warnings:")
        for warning in health_check["warnings"]:
            print(f"   - {warning}")

        # Ask user if they want to continue
        response = input("Continue with backup? (y/N): ").lower().strip()
        if response not in ['y', 'yes']:
            return False

    if health_check["recommendations"]:
        print("RECOMMENDATIONS:")
        for rec in health_check["recommendations"]:
            print(f"   - {rec}")
        print()
    
    return True


def show_usage():
    """Show detailed usage information."""
    print("""
TNGD Backup System v2.0
========================

A professional backup system with advanced resource management,
monitoring, and recovery capabilities.

NOTE: Historical backup functionality has been removed.
The system now backs up ALL available data from ALL tables.

Usage:
    python -m tngd_backup.main [OPTIONS]

Options:
    -h, --help                  Show this help message
    --config PATH               Configuration file path
    --check-only                Only check system readiness
    --dry-run                   Simulate backup without actual execution
    --verbose                   Enable verbose logging

Examples:
    python -m tngd_backup.main
    python -m tngd_backup.main --config config/production.json
    python -m tngd_backup.main --production
    python -m tngd_backup.main --developer

Features:
    ✓ Thread management (max 4 threads)
    ✓ Memory optimization (1500MB limit)
    ✓ Automatic retry and recovery
    ✓ Real-time progress monitoring
    ✓ Checkpoint system for resume
    ✓ Resource monitoring and alerts
    ✓ Comprehensive error handling
    ✓ OSS storage with compression
    ✓ Complete data backup (all available rows)

For more information, see docs/usage.md
""")


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        description="TNGD Backup System v2.0 - Complete Data Backup",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                           # Backup all data
  %(prog)s --production              # Backup all data (production config)
  %(prog)s --developer               # Backup all data (developer config)
        """
    )
    parser.add_argument(
        '--config',
        help='Configuration file path'
    )
    parser.add_argument(
        '--production',
        action='store_true',
        help='Use production configuration (all tables, optimized settings)'
    )
    parser.add_argument(
        '--developer',
        action='store_true',
        help='Use developer configuration (subset of tables, faster testing)'
    )
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='Only check system readiness'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Simulate backup without actual execution'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    parser.add_argument(
        '--usage',
        action='store_true',
        help='Show detailed usage information'
    )

    return parser


def setup_logging(verbose: bool) -> None:
    """Setup logging configuration."""
    if verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)


def validate_system_and_dates(args) -> tuple:
    """Validate system readiness and prepare backup run."""
    # Validate configuration arguments
    if args.production and args.developer:
        print("ERROR: Cannot specify both --production and --developer flags")
        return False, None

    # Check system readiness
    if not check_system_readiness():
        print("ERROR: System not ready for backup operations")
        return False, None

    if args.check_only:
        print("SUCCESS: System ready for backup operations")
        return True, None

    # Create backup run timestamp (no date parsing needed)
    backup_run = parse_dates([])  # Returns current timestamp
    return True, backup_run


def display_backup_info(backup_run: List[datetime], args) -> None:
    """Display backup information to user."""
    print("TNGD Backup System v2.0")
    print("=" * 40)
    print("Mode: COMPLETE DATA BACKUP")
    print("Scope: ALL TABLES, ALL AVAILABLE DATA")
    print(f"Backup Run ID: {backup_run[0].strftime('%Y%m%d_%H%M%S')}")

    if args.config:
        print(f"Config: {args.config}")

    if args.dry_run:
        print("Mode: DRY RUN (simulation only)")

    print()


def execute_backup_workflow(backup_run: List[datetime], args) -> int:
    """Execute the backup workflow and return exit code."""
    # Determine configuration file based on flags
    config_file = args.config
    if args.production:
        config_file = "config/production.json"
        print("PRODUCTION: Using production configuration")
    elif args.developer:
        config_file = "config/development.json"
        print("DEVELOPER: Using developer configuration")
    elif not config_file:
        config_file = "config/default.json"
        print("DEFAULT: Using default configuration")

    # Create and run backup engine
    engine = BackupEngine(backup_run, config_file)

    if args.dry_run:
        print("SUCCESS: Dry run completed - no actual backup performed")
        return 0

    # Run backup
    results = engine.run_backup()

    # Show results
    print("\n" + "=" * 50)
    if results['status'] == 'completed':
        print("SUCCESS: Complete data backup finished!")
        print(f"Total operations: {results['metrics']['total_tables']}")
        print(f"Completed: {results['metrics']['completed_tables']}")
        print(f"Failed: {results['metrics']['failed_tables']}")
        print(f"Total rows: {results['metrics']['total_rows']:,}")
        print(f"Duration: {results['metrics']['total_duration']/60:.1f} minutes")
        return 0
    else:
        print("ERROR: Backup failed!")
        if results.get('error'):
            print(f"Error: {results['error']}")
        return 1


def main():
    """Main entry point for TNGD backup system."""
    parser = create_argument_parser()
    args = parser.parse_args()

    # Show usage if requested
    if args.usage:
        show_usage()
        return 0

    # Setup logging
    setup_logging(args.verbose)

    try:
        # Validate system and prepare backup run
        is_valid, backup_run = validate_system_and_dates(args)
        if not is_valid:
            return 1

        # Handle check-only mode
        if args.check_only:
            return 0

        # Display backup information
        display_backup_info(backup_run, args)

        # Execute backup workflow
        return execute_backup_workflow(backup_run, args)

    except KeyboardInterrupt:
        print("\nBackup interrupted by user")
        return 1
    except Exception as unexpected_error:
        print(f"Unexpected error: {unexpected_error}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
