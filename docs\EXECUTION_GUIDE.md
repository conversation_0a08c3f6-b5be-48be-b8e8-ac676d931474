# TNGD Backup System - Complete Execution Guide

## 🚀 Quick Start

The TNGD Backup System now performs **complete data backup** - it backs up ALL available data from ALL tables in a single operation.

### Basic Execution
```bash
# Navigate to project directory
cd c:\Users\<USER>\Desktop\TNGD

# Run complete backup with default settings
python run_backup.py
```

## 📋 Prerequisites

### 1. **Python Environment**
- Python 3.8 or higher
- All dependencies installed

### 2. **Dependencies Installation**
```bash
# Install required packages
pip install -r requirements.txt
```

### 3. **Environment Configuration**
- `.env` file is already configured with:
  - ✅ Devo API credentials
  - ✅ Alibaba Cloud OSS credentials  
  - ✅ Email notification settings

## 🎯 Execution Options

### **Option 1: Default Mode (Recommended)**
```bash
python run_backup.py
```
- Uses default configuration
- Backs up all tables from `config/tables.json`
- Sends email report when complete

### **Option 2: Production Mode**
```bash
python run_backup.py --production
```
- Uses `config/production.json`
- Processes all 65 tables
- Optimized settings for large datasets
- Best for scheduled/automated backups

### **Option 3: Developer Mode**
```bash
python run_backup.py --developer
```
- Uses `config/development.json`
- Processes subset of tables for testing
- Faster execution for development/testing

### **Option 4: Custom Configuration**
```bash
python run_backup.py --config config/custom.json
```
- Uses your custom configuration file
- Allows fine-tuned control

### **Option 5: System Check Only**
```bash
python run_backup.py --check-only
```
- Validates system readiness
- Checks credentials and connectivity
- No actual backup performed

## 📊 What Happens During Execution

### **1. System Initialization**
```
[START] Starting TNGD Backup System v2.0...
[INFO] Mode: COMPLETE DATA BACKUP (all tables, all available data)
PRODUCTION: Mode selected - will process all 65 tables
```

### **2. System Validation**
- ✅ Checks Devo API connectivity
- ✅ Validates OSS storage access
- ✅ Verifies email configuration
- ✅ Loads table configuration

### **3. Backup Execution**
```
=== TNGD BACKUP ENGINE STARTED ===
Mode: COMPLETE DATA BACKUP (all tables, all available data)
Processing 65 tables for complete data backup
```

### **4. Progress Monitoring**
- Real-time progress updates
- Resource usage monitoring
- Error handling and retries
- Performance metrics

### **5. Results Summary**
```
SUCCESS: Complete data backup finished!
Total operations: 65
Completed: 63
Failed: 2
Total rows: 1,234,567
Duration: 45.2 minutes
```

## 📁 Output Structure

### **Storage Location**
```
OSS Bucket: siem-security-logs
Path: Devo/complete_backup/{backup_run_id}/
```

### **File Structure**
```
complete_backup/
├── 20250714_152503/
│   ├── my_app_tngd_actiontraillinux.tar.gz
│   ├── my_app_tngd_actiontrailwindows.tar.gz
│   ├── cloud_alibaba_log_service_events.tar.gz
│   ├── cef0_zscaler_nssweblog.tar.gz
│   └── ... (all other tables)
```

### **Local Files**
```
data/
├── logs/
│   └── tngd_backup_2025-07-14_15-28-52_2025-07-14.log
├── exports/
│   └── 20250714_152503/
│       └── (temporary files, cleaned up after upload)
└── results/
    └── backup_summary_20250714_152503.json
```

## 📧 Email Reports

After completion, you'll receive an email report with:
- ✅ Backup summary and statistics
- ✅ Table-by-table results
- ✅ Performance metrics
- ✅ Any errors or warnings
- ✅ Download links (if applicable)

## 🔧 Advanced Usage

### **Direct Module Execution**
```bash
# Alternative execution method
python -m tngd_backup.main --production
```

### **With Verbose Logging**
```bash
python run_backup.py --production --verbose
```

### **Resource Monitoring**
```bash
# Run resource monitor in separate terminal
python resource_monitor_dashboard.py
```

## 🚨 Troubleshooting

### **Common Issues**

#### **1. Credential Errors**
```bash
# Check system connectivity
python run_backup.py --check-only
```

#### **2. Permission Issues**
```bash
# Run as administrator if needed
# Right-click PowerShell -> "Run as Administrator"
```

#### **3. Memory Issues**
```bash
# Use production config with optimized settings
python run_backup.py --production
```

#### **4. Network Issues**
- Check internet connectivity
- Verify firewall settings
- Confirm VPN if required

### **Log Analysis**
```bash
# Check latest log file
type "data\logs\tngd_backup_*.log"
```

## 📈 Performance Expectations

### **Typical Performance**
- **Small tables** (1-100 rows): 1-5 seconds each
- **Medium tables** (100-1000 rows): 5-30 seconds each  
- **Large tables** (1000+ rows): 30-300 seconds each

### **Total Duration**
- **Developer mode**: 5-15 minutes
- **Production mode**: 30-90 minutes
- **Depends on**: Data volume, network speed, system resources

## 🔄 Scheduling (Optional)

### **Windows Task Scheduler**
```bash
# Create scheduled task
schtasks /create /tn "TNGD Backup" /tr "python c:\Users\<USER>\Desktop\TNGD\run_backup.py --production" /sc daily /st 02:00
```

### **Cron (Linux/Mac)**
```bash
# Add to crontab
0 2 * * * cd /path/to/TNGD && python run_backup.py --production
```

## 📋 Pre-Execution Checklist

- [ ] Python 3.8+ installed
- [ ] Dependencies installed (`pip install -r requirements.txt`)
- [ ] `.env` file configured
- [ ] Internet connectivity available
- [ ] Sufficient disk space (at least 5GB free)
- [ ] Email credentials working (if notifications desired)

## 🎯 Success Indicators

### **Successful Execution**
- ✅ No error messages in console
- ✅ "SUCCESS: Complete data backup finished!" message
- ✅ Email report received
- ✅ Files uploaded to OSS storage
- ✅ Log file shows completed status

### **Verification Steps**
1. Check console output for success message
2. Verify email report received
3. Check OSS bucket for uploaded files
4. Review log file for any warnings

## 📞 Support

If you encounter issues:
1. Check the log file in `data/logs/`
2. Run with `--check-only` to validate system
3. Try `--developer` mode for faster testing
4. Review error messages for specific issues

---

## 🚀 **Ready to Execute?**

```bash
# Navigate to project directory
cd c:\Users\<USER>\Desktop\TNGD

# Run complete backup
python run_backup.py --production
```

The system will handle everything automatically and provide you with a complete backup of all your data!
