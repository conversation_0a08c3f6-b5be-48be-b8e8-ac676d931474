# TNGD Backup System - Quick Reference

## 🚀 **How to Run**

```bash
# Navigate to project
cd c:\Users\<USER>\Desktop\TNGD

# Choose one execution method:
python run_backup.py                    # Default mode
python run_backup.py --production       # Production mode (recommended)
python run_backup.py --developer        # Developer mode (testing)
python run_backup.py --check-only       # System check only
```

## 📊 **Execution Modes**

| Mode | Command | Tables | Duration | Use Case |
|------|---------|--------|----------|----------|
| **Default** | `python run_backup.py` | All tables | 30-60 min | Regular backup |
| **Production** | `python run_backup.py --production` | All 65 tables | 45-90 min | Scheduled backup |
| **Developer** | `python run_backup.py --developer` | Subset | 5-15 min | Testing |
| **Check Only** | `python run_backup.py --check-only` | None | 1-2 min | Validation |

## 📁 **What Gets Backed Up**

- ✅ **ALL available data** from ALL tables
- ✅ **Complete raw data** (no date filtering)
- ✅ **All rows** from each table
- ✅ **Compressed archives** (.tar.gz format)

## 📍 **Where Data Goes**

- **Cloud Storage**: Alibaba Cloud OSS bucket `siem-security-logs`
- **Path**: `Devo/complete_backup/{timestamp}/`
- **Local Logs**: `data/logs/tngd_backup_*.log`

## 🎯 **Success Indicators**

```bash
SUCCESS: Complete data backup finished!
Total operations: 65
Completed: 63
Failed: 2
Total rows: 1,234,567
Duration: 45.2 minutes
```

## 📧 **Email Report**

You'll receive an email with:
- Backup summary
- Table-by-table results  
- Performance metrics
- Any errors/warnings

## 🔧 **Troubleshooting**

| Issue | Solution |
|-------|----------|
| Credential errors | `python run_backup.py --check-only` |
| Memory issues | Use `--production` mode |
| Network issues | Check internet/VPN |
| Permission issues | Run as administrator |

## 📋 **Prerequisites**

- [x] Python 3.8+
- [x] Dependencies: `pip install -r requirements.txt`
- [x] `.env` file configured (✅ already done)
- [x] Internet connectivity

## ⚡ **Quick Start**

```bash
# 1. Open PowerShell/Terminal
# 2. Navigate to project
cd c:\Users\<USER>\Desktop\TNGD

# 3. Run backup
python run_backup.py --production

# 4. Wait for completion (30-90 minutes)
# 5. Check email for report
```

## 📊 **Expected Output**

```
[START] Starting TNGD Backup System v2.0...
[INFO] Mode: COMPLETE DATA BACKUP (all tables, all available data)
PRODUCTION: Mode selected - will process all 65 tables

=== TNGD BACKUP ENGINE STARTED ===
Processing 65 tables for complete data backup

Processing table: my.app.tngd.actiontraillinux (complete data)
Processing table: my.app.tngd.actiontrailwindows (complete data)
...

SUCCESS: Complete data backup finished!
```

---

## 🚀 **Ready? Just run:**
```bash
python run_backup.py --production
```
