{"timestamp": "2025-07-02T20:00:39.910794", "tables_investigated": {"my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "date_fields": [{"field_name": "eventdate", "field_value": 1750809600123, "field_type": "int", "is_numeric": true}, {"field_name": "raw_message___time__", "field_value": "1750528337", "field_type": "str", "is_numeric": false}, {"field_name": "raw_message_origin_time", "field_value": "1750528337921123", "field_type": "str", "is_numeric": false}, {"field_name": "raw_message_update_rows", "field_value": "1", "field_type": "str", "is_numeric": false}], "sample_data": {"eventdate": 1750809600123, "cluster": "-", "instance": "-", "raw_message": "{\"thread_id\":\"15069832\",\"db_role\":\"polardb_mysql_rw\",\"client_ip\":\"**************\",\"polardb_type\":\"MySQL\",\"fail\":\"0\",\"db_version\":\"5.6\",\"check_rows\":\"0\",\"cluster_id\":\"pc-zf8dd955r7xr2huf7\",\"polardb_category\":\"Normal\",\"update_rows\":\"1\",\"owner_id\":\"5478802082436037\",\"__topic__\":\"polardb_audit_log\",\"return_rows\":\"0\",\"__source__\":\"log_service\",\"user\":\"batch_event2\",\"db\":\"glotng_prd\",\"latency\":\"132\",\"sql\":\"INSERT into BATCH_JOB_EXECUTION(JOB_EXECUTION_ID, JOB_INSTANCE_ID, START_TIME, END_TIME, STATUS, EXIT_CODE, EXIT_MESSAGE, VERSION, CREATE_TIME, LAST_UPDATED) values (2173726685, 2173705477, null, null, 'STARTING', 'UNKNOWN', '', 0, '2025-06-22 01:52:17.92', '2025-06-22 01:52:17.92')\",\"origin_time\":\"1750528337921123\",\"__time__\":\"1750528337\",\"node_id\":\"pi-zf83e8s8ggp789u09\"}", "raw_message___time__": "1750528337", "raw_message___topic__": "polardb_audit_log", "raw_message___source__": "log_service", "raw_message_owner_id": "5478802082436037", "raw_message_cluster_id": "pc-zf8dd955r7xr2huf7", "raw_message_polardb_category": "Normal", "raw_message_polardb_type": "MySQL", "raw_message_db_version": "5.6", "raw_message_check_rows": "0", "raw_message_db": "glotng_prd", "raw_message_fail": "0", "raw_message_client_ip": 176868025, "raw_message_latency": "132", "raw_message_origin_time": "1750528337921123", "raw_message_return_rows": "0", "raw_message_sql": "INSERT into BATCH_JOB_EXECUTION(JOB_EXECUTION_ID, JOB_INSTANCE_ID, START_TIME, END_TIME, STATUS, EXIT_CODE, EXIT_MESSAGE, VERSION, CREATE_TIME, LAST_UPDATED) values (2173726685, 2173705477, null, null, 'STARTING', 'UNKNOWN', '', 0, '2025-06-22 01:52:17.92', '2025-06-22 01:52:17.92')", "raw_message_thread_id": "15069832", "raw_message_update_rows": "1", "raw_message_user": "batch_event2", "raw_message_node_id": "pi-zf83e8s8ggp789u09", "raw_message_db_role": "polardb_mysql_rw"}, "issues": ["eventdate is a timestamp (1750809600123) that converts to 2025-06-25"], "eventdate_analysis": {"value": 1750809600123, "type": "int", "is_numeric": true, "is_timestamp": true, "converted_date": "2025-06-25"}, "filtering_tests": {"no_filter_error": "Error executing query: Error Launching Query: No function named `count` can be called at stage 1: 1101010: QUERY_PARSING_ERROR: NO", "standard_filter_error": "'total'", "timestamp_filter_error": "'total'", "date_function_error": "'total'"}}, "cloud.office365.management.exchange": {"table_name": "cloud.office365.management.exchange", "date_fields": [{"field_name": "eventdate", "field_value": 1750809645866, "field_type": "int", "is_numeric": true}, {"field_name": "StatusTime", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "timestamp", "field_value": 1750809075000, "field_type": "int", "is_numeric": true}, {"field_name": "Messages_PublishedTime_str", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "EndTime", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "Schedules_Time_str", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "Schedules_TimeZone", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "StartTime", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "LastUpdatedTime", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "MessageTime", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "EndTimeUtc", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "LastUpdateTimeUtc", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "StartTimeUtc", "field_value": null, "field_type": "NoneType", "is_numeric": false}], "sample_data": {"eventdate": 1750809645866, "hostname": "collector-ade97a6552c21007-5595b6b96-rsbx7", "Id": "185fee74-6f48-4bab-9797-1e8f67a6c51e", "Workload": "Exchange", "StatusTime": null, "FeatureStatus": null, "Status": null, "StatusDisplayName": null, "IncidentIds": null, "WorkloadDisplayName": null, "UserType": 0, "timestamp": 1750809075000, "Operation": "MailItemsAccessed", "Version": 1, "LogonType": 0, "MailboxOwnerSid": "S-1-5-21-990976496-384743642-898738496-68155419", "ExternalAccess": false, "OrganizationName": "tngd.onmicrosoft.com", "SessionId": null, "ClientAddress": "**************", "ClientIPAddress": "**************", "ClientProcessName": null, "ResultStatus": "Succeeded", "UserId": "<EMAIL>", "LogonUserSid": "S-1-5-21-990976496-384743642-898738496-68155419", "InternalLogonType": 0, "OriginatingServer": "TY0PR03MB8247 (15.20.4200.000)\r\n", "UserKey": "10032004607F1C18", "MailboxGuid": "0813b41d-d253-45b8-8b14-16d0dec536f1", "OrganizationId": "5f7e89be-4439-4290-a40f-c043c710bb8c", "RecordType": 50, "ClientInfoString": "Client=OutlookService;Outlook-iOS/2.0;", "MailboxOwnerUPN": "<EMAIL>", "CrossMailboxOperation": null, "AffectedItems": null, "Folder_Id": null, "Folder_Path": null, "Folders": "[{\"Path\":\"\\\\Inbox\",\"FolderItems\":[{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTFEmAAAJ\",\"SizeInBytes\":236573,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTFEmAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfgAAAJ\",\"SizeInBytes\":666425,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfgAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfOAAAJ\",\"SizeInBytes\":611855,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfOAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+0AAAJ\",\"SizeInBytes\":2484144,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+0AAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+cAAAJ\",\"SizeInBytes\":758335,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+cAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXwAAAJ\",\"SizeInBytes\":746513,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXwAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXaAAAJ\",\"SizeInBytes\":213869,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXaAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXLAAAJ\",\"SizeInBytes\":110687,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXLAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDW+AAAJ\",\"SizeInBytes\":188724,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDW+AAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTC3ZAAAJ\",\"SizeInBytes\":3178929,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTC3ZAAAJ\"},{\"ImmutableId\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTCfwAAAJ\",\"SizeInBytes\":845923,\"InternetMessageId\":\"<<EMAIL>>\",\"Id\":\"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTCfwAAAJ\"}],\"Id\":\"LgAAAAAY9DVr7+zWTa0BY630UhWHAQAcoo1FOQqWRZdxd2MMJjvMAAAAAAEMAAAB\"}]", "FoldersItemsStr": "[{\"FolderItems\": [{\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTFEmAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTFEmAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 236573}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfgAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfgAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 666425}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfOAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfOAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 611855}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+0AAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+0AAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 2484144}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+cAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+cAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 758335}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXwAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXwAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 746513}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXaAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXaAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 213869}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXLAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXLAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 110687}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDW+AAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDW+AAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 188724}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTC3ZAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTC3ZAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 3178929}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTCfwAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTCfwAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 845923}], \"Id\": \"LgAAAAAY9DVr7+zWTa0BY630UhWHAQAcoo1FOQqWRZdxd2MMJjvMAAAAAAEMAAAB\", \"Path\": \"\\\\Inbox\"}]", "Folders__Id_str": null, "Folders__Path_str": null, "Folders__FolderItems_ClientRequestId_str": "", "Folders__FolderItems_InternetMessageId_str": "<<EMAIL>>", "Folders__FolderItems_SizeInBytes_str": "", "ForwardTo": null, "Parameters_Raw": null, "Item_Subject": null, "Item_Attachments": null, "Item_ParentFolder_Id": null, "Item_ParentFolder_Path": null, "ModifiedProperties": null, "SendOnBehalfOfUserSmtp": null, "SendAsUserSmtp": null, "PolicyDetails": null, "PolicyDetails_PolicyName_str": null, "PolicyDetails_PolicyId_str": null, "PolicyDetails_location_str": null, "PolicyDetails_RuleMode_str": null, "PolicyDetails_RuleName_str": null, "PolicyDetails_RuleId_str": null, "PolicyDetails_Severity_str": null, "PolicyDetails_ManagementRuleId_str": null, "Unique_PolicyDetails_location_str": null, "PolicyDetails_confidence_str": "null", "PolicyDetails_count_str": "null", "PolicyDetails_sensitiveType_str": null, "PolicyDetails_uniqueCount_str": "null", "PolicyDetails_ConditionsMatched_Name_str": null, "PolicyDetails_ConditionsMatched_Value_str": null, "PolicyDetails_ConditionMatchedInNewScheme_str": "null", "ExchangeMetaData_BCC": null, "ExchangeMetaData_MessageID": null, "ExchangeMetaData_From": null, "ExchangeMetaData_CC": null, "ExchangeMetaData_Sent": null, "ExchangeMetaData_Subject": null, "ExchangeMetaData_RecipientCount": null, "ExchangeMetaData_To": null, "InterSystemsId": null, "TargetUserId": null, "Actor_ID_str": null, "Actor_Type_str": "null", "ActorContextId": null, "YammerNetworkId": null, "ActorUserId": null, "ActorIpAddress": null, "Client": null, "ClientIP": null, "LogonError": null, "ApplicationId": null, "Target_ID_str": null, "Target_Type_str": "null", "IntraSystemId": null, "ExtendedProperties_Name_str": null, "ExtendedProperties_Value_str": null, "ActorYammerUserId": null, "FileName": null, "TargetContextId": null, "AzureActiveDirectoryEventType": null, "VersionId": null, "FileId": null, "PostIncidentDocumentUrl": null, "Severity": null, "Title": null, "Comments": null, "AffectedWorkloadDisplayNames": null, "AlertEntityId": null, "Messages_MessageText_str": null, "Messages_PublishedTime_str": null, "ChannelGuid": null, "LogonUserDisplayName": null, "RecipientUPN": null, "ApplicationDisplayName": null, "MessageType": null, "EventSource": null, "DestinationRelativeUrl": null, "MachineId": null, "WebId": null, "SendOnBehalfOfUserMailboxGuid": null, "ExtraProperties_Key_str": null, "ExtraProperties_Value_str": null, "SharingPermission": null, "ObjectName": null, "SharingType": null, "DataflowRefreshScheduleType": null, "TenantName": null, "CustomUniqueId": null, "DatasetId": null, "SiteUrl": null, "Parameters_Name_str": null, "Parameters_Value_str": null, "Parameters_Identity": null, "Parameters_AccessRights": null, "Parameters_User": null, "Parameters_ForwardingAddress": null, "Parameters_DeliverToMailboxAndForward": null, "ImportType": null, "ImportId": null, "PolicyId": null, "ItemName": null, "Datasets_DatasetId_str": null, "Datasets_DatasetName_str": null, "ImplicitShare": null, "ImportDisplayName": null, "ItemType": null, "WorkSpaceName": null, "DestFolder_Path": null, "DestFolder_Id": null, "UniqueSharingId": null, "TargetUserOrGroupName": null, "FlowConnectorNames": null, "FileSyncBytesCommitted": null, "CorrelationId": null, "Members_DisplayName_str": null, "Members_UPN_str": null, "Members_Role_str": "null", "AddOnGuid": null, "DashboardName": null, "IsSuccess": null, "AlertId": null, "ListTitle": null, "ReportType": null, "AffectedWorkloadNames": null, "FlowDetailsUrl": null, "TargetYammerUserId": null, "ImpactDescription": null, "BrowserName": null, "OperationProperties_Value_str": "Bind", "OperationProperties_Name_str": "MailAccessType", "ReportId": null, "DestMailboxOwnerSid": null, "DestMailboxOwnerMasterAccountSid": null, "AffectedUserCount": null, "Category": null, "MachineDomainInfo": null, "ListBaseType": null, "DestMailboxId": null, "TabType": null, "Activity": null, "DestinationFileExtension": null, "UserUPN": null, "ListId": null, "SourceRelativeUrl": null, "UserTypeInitiated": null, "EndTime": null, "SendAsUserMailboxGuid": null, "ActionType": null, "SourceFileExtension": null, "DashboardId": null, "ClientApplicationId": null, "DestMailboxOwnerUPN": null, "MailboxOwnerMasterAccountSid": null, "SensitiveInfoDetectionIsIncluded": null, "Schedules_RefreshFrequency": null, "Schedules_Days_str": null, "Schedules_Time_str": null, "Schedules_TimeZone": null, "TeamName": null, "WorkspaceId": null, "DataflowType": null, "SourceFileName": null, "FeatureDisplayName": null, "EntityPath": null, "TeamGuid": null, "ResourceTitle": null, "Classification": null, "ListBaseTemplateType": null, "DestinationFileName": null, "AffectedTenantCount": null, "DatasetName": null, "LicenseDisplayName": null, "Feature": null, "StartTime": null, "TargetUserOrGroupType": null, "DataConnectivityMode": null, "LastUpdatedTime": null, "ReportName": null, "EntityType": null, "OperationDetails": null, "UserAgent": null, "AlertType": null, "Name": null, "CmdletVersion": null, "ImportSource": null, "SkypeForBusinessEventType": null, "AddOnType": null, "DoNotDistributeEvent": null, "ChannelName": null, "ListItemUniqueId": null, "ObjectId": null, "AttachmentData": null, "DeliveryAction": null, "DetectionMethod": null, "DetectionType": null, "Directionality": null, "EventDeepLink": null, "InternetMessageId": null, "LatestDeliveryLocation": null, "MessageTime": null, "NetworkMessageId": null, "OriginalDeliveryLocation": null, "P1Sender": null, "P2Sender": null, "Policy": null, "PolicyAction": null, "Recipients": null, "SenderIp": null, "Subject": null, "ThreatsAndDetectionTech": null, "Verdict": null, "SourceLocationType": null, "Platform": null, "Application": null, "FileExtension": null, "DeviceName": null, "MDATPDeviceId": null, "FileSize": null, "FileType": null, "Hidden": null, "Actions": null, "AlertLinks": null, "Data": null, "DeepLinkUrl": null, "EndTimeUtc": null, "InvestigationId": null, "InvestigationName": null, "InvestigationType": null, "LastUpdateTimeUtc": null, "StartTimeUtc": null, "Source": null, "message": "{\"AppAccessContext\": {\"APIId\": \"\"}, \"CreationTime\": \"2025-06-24T23:51:15\", \"Id\": \"185fee74-6f48-4bab-9797-1e8f67a6c51e\", \"Operation\": \"MailItemsAccessed\", \"OrganizationId\": \"5f7e89be-4439-4290-a40f-c043c710bb8c\", \"RecordType\": 50, \"ResultStatus\": \"Succeeded\", \"UserKey\": \"10032004607F1C18\", \"UserType\": 0, \"Version\": 1, \"Workload\": \"Exchange\", \"UserId\": \"<EMAIL>\", \"ClientIPAddress\": \"**************\", \"ClientInfoString\": \"Client=OutlookService;Outlook-iOS/2.0;\", \"ExternalAccess\": false, \"InternalLogonType\": 0, \"LogonType\": 0, \"LogonUserSid\": \"S-1-5-21-990976496-384743642-898738496-68155419\", \"MailboxGuid\": \"0813b41d-d253-45b8-8b14-16d0dec536f1\", \"MailboxOwnerSid\": \"S-1-5-21-990976496-384743642-898738496-68155419\", \"MailboxOwnerUPN\": \"<EMAIL>\", \"OperationProperties\": [{\"Name\": \"MailAccessType\", \"Value\": \"Bind\"}], \"OrganizationName\": \"tngd.onmicrosoft.com\", \"OriginatingServer\": \"TY0PR03MB8247 (15.20.4200.000)\\r\\n\", \"Folders\": [{\"FolderItems\": [{\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTFEmAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTFEmAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 236573}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfgAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfgAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 666425}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfOAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTEfOAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 611855}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+0AAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+0AAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 2484144}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+cAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTD+cAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 758335}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXwAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXwAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 746513}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXaAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXaAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 213869}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXLAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDXLAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 110687}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDW+AAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTDW+AAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 188724}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTC3ZAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTC3ZAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 3178929}, {\"Id\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTCfwAAAJ\", \"ImmutableId\": \"LgAAAAAdhAMRqmYRzZvIAKoAL8RaDQAcoo1FOQqWRZdxd2MMJjvMAABGTCfwAAAJ\", \"InternetMessageId\": \"<<EMAIL>>\", \"SizeInBytes\": 845923}], \"Id\": \"LgAAAAAY9DVr7+zWTa0BY630UhWHAQAcoo1FOQqWRZdxd2MMJjvMAAAAAAEMAAAB\", \"Path\": \"\\\\Inbox\"}], \"OperationCount\": 11, \"@devo_content_record_hash\": \"abbdd3ade14c412bf3911084361fedfaf4727af4be0e56a6b359c14e394034f473e41fc0a9630f1a15ddd7e17a496fc996f2d3ee0dc8dc8477ea95fe0b77cf0a\", \"@devo_content_id\": \"20250624235320353001560$20250624235621043002060$audit_exchange$Audit_Exchange$sea0013\", \"@devo_content_created\": \"2025-06-24T23:56:21.043Z\", \"@devo_content_expiration\": \"2025-07-01T23:53:20.353Z\", \"@devo_environment\": \"Production\"}"}, "issues": ["eventdate is a timestamp (1750809645866) that converts to 2025-06-25"], "eventdate_analysis": {"value": 1750809645866, "type": "int", "is_numeric": true, "is_timestamp": true, "converted_date": "2025-06-25"}, "filtering_tests": {"no_filter_error": "Error executing query: Error Launching Query: No function named `count` can be called at stage 1: 1101010: QUERY_PARSING_ERROR: NO", "standard_filter_error": "'total'", "timestamp_filter_error": "'total'", "date_function_error": "'total'"}}, "firewall.fortinet.traffic.forward": {"table_name": "firewall.fortinet.traffic.forward", "date_fields": [{"field_name": "eventdate", "field_value": 1750809600162, "field_type": "int", "is_numeric": true}, {"field_name": "serverdate", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "servertime", "field_value": "07:59:59", "field_type": "str", "is_numeric": false}, {"field_name": "timestamp", "field_value": null, "field_type": "NoneType", "is_numeric": false}, {"field_name": "eventtime", "field_value": 1750809598632551709, "field_type": "int", "is_numeric": true}, {"field_name": "serverdatetime", "field_value": 1750809598632, "field_type": "int", "is_numeric": true}], "sample_data": {"eventdate": 1750809600162, "machine": "date", "serverdate": null, "servertime": "07:59:59", "timestamp": null, "tz": "+0800", "priority": null, "level": "notice", "devName": "FW-TNGD-L5-1", "devID": "FG4H0FT923914848", "logver": null, "virtDomain": "root", "eventtime": 1750809598632551709, "serverdatetime": 1750809598632, "user": null, "status": null, "session": 403535298, "duration": 4501, "action": "accept", "rule": null, "policyID": 226, "proto": 6, "protoStr": "TCP", "service": "HTTPS", "appType": null, "srcIp": 169108623, "srcIp_str": "*************", "srcHost": "TNGD-REM-MINIPC", "srcPort": 63366, "dstIp": 2755253126, "dstIp_str": "**************", "dstHost": null, "dstPort": 443, "srcIface": "IoT_WIFI", "dstIface": "port3", "srcintfrole": "lan", "dstintfrole": "wan", "bytesSent": 20471, "bytesRecv": 15737, "pktsSent": 166, "pktsRecv": 165, "vpnName": null, "dstdevtype": null, "natIp": 3524525034, "natPort": 63366, "natType": "snat", "srcCountry": "Reserved", "dstCountry": "United States", "crScore": null, "crAction": null, "poluuid": "2c123854-b96c-51ef-9f0c-817e6f646e34", "policyName": "IoT-to-internet", "policyType": "policy", "appID": null, "appCat": "unscanned", "appRisk": null, "appList": null, "utmAction": null, "countApp": null, "wanin": null, "wanout": null, "lanin": null, "lanout": null, "countweb": null, "crlevel": null, "tranip": null, "tranport": null, "wanoptapptype": null, "countav": null, "logID": "0000000020", "identifier": null, "apsn": null, "ap": null, "channel": null, "radioband": null, "signal": null, "snr": null, "srcmac": "30:f6:ef:07:81:22", "srcserver": "0", "srcssid": null, "srcfamily": null, "srchwvendor": null, "srchwversion": null, "srcswversion": "10", "dsthwvendor": null, "dstmac": null, "dstserver": null, "mastersrcmac": "30:f6:ef:07:81:22", "masterdstmac": null, "vpntype": null, "vwlid": "0", "authserver": null, "devtype": null, "dstfamily": null, "dsthwversion": null, "dstosname": null, "dstswversion": null, "dstunauthuser": null, "dstunauthusersource": null, "dstuser": null, "osname": "Windows", "pdstport": null, "psrcport": null, "rcvddelta": 288, "sentdelta": 162, "shaperdroprcvdbyte": null, "shaperdropsentbyte": null, "shaperrcvdname": null, "shapersentname": null, "shapingpolicyid": null, "shapingpolicyname": null, "unauthuser": null, "unauthusersource": null, "vip": null, "countips": null, "countssh": null, "countssl": null, "url": null, "message": null}, "issues": ["eventdate is a timestamp (1750809600162) that converts to 2025-06-25"], "eventdate_analysis": {"value": 1750809600162, "type": "int", "is_numeric": true, "is_timestamp": true, "converted_date": "2025-06-25"}, "filtering_tests": {"no_filter_error": "Error executing query: Error Launching Query: No function named `count` can be called at stage 1: 1101010: QUERY_PARSING_ERROR: NO", "standard_filter_error": "'total'", "timestamp_filter_error": "'total'", "date_function_error": "'total'"}}}, "summary": {"total_tables": 3, "tables_with_timestamp_eventdate": 3, "tables_with_string_eventdate": 0, "tables_missing_eventdate": 0, "recommended_fixes": ["Update backup system to handle timestamp-based eventdate fields", "Modify date filtering logic to convert dates to timestamp ranges"]}}