#!/usr/bin/env python3
"""
TNGD Backup System Constants

This module contains all constants and configuration values used throughout
the TNGD backup system to eliminate magic numbers and improve maintainability.
"""

from typing import Dict, Any


class BackupConstants:
    """Core backup operation constants."""
    
    # Query and Processing Constants
    DEFAULT_CHUNK_SIZE = 50000  # Default number of rows per chunk
    DEFAULT_TIMEOUT_SECONDS = 1800  # 30 minutes default timeout
    DEFAULT_MAX_RETRIES = 3  # Default number of retry attempts

    # Data Processing Constants
    LARGE_CHUNK_SIZE = 500000  # Large chunk size for file operations
    STREAMING_CHUNK_SIZE = 100000  # Streaming chunk size
    PROGRESS_LOG_INTERVAL = 100000  # Log progress every N rows
    MILESTONE_LOG_INTERVAL = 1000000  # Log milestones every N rows
    HEARTBEAT_LOG_INTERVAL = 10000  # Heartbeat logging interval
    SMALL_DATA_THRESHOLD = 1000  # Threshold for small data logging
    MEDIUM_DATA_THRESHOLD = 100000  # Threshold for medium data logging

    # Query Limits and Validation
    MAX_QUERY_LIMIT = 10000000  # Maximum allowed query limit
    DEFAULT_ROW_ESTIMATE = 10000  # Default row estimate per day
    
    # Storage Constants
    DEFAULT_CHUNK_SIZE_MB = 100  # Default chunk size in MB for uploads
    DEFAULT_MEMORY_THRESHOLD_PERCENT = 75  # Memory usage threshold
    DEFAULT_UPLOAD_TIMEOUT_SECONDS = 1800  # Upload timeout (30 minutes)
    DEFAULT_RETRY_DELAY_SECONDS = 5  # Delay between retries
    
    # Thread Management Constants
    DEFAULT_MAX_THREADS = 8  # Maximum number of threads
    DEFAULT_CLEANUP_INTERVAL_SECONDS = 30  # Thread cleanup interval
    
    # Monitoring Constants
    DEFAULT_MONITOR_INTERVAL_SECONDS = 30  # Resource monitoring interval
    DEFAULT_MAX_HISTORY_ENTRIES = 100  # Maximum health history entries
    MEMORY_CHECK_INTERVAL_SECONDS = 30  # Memory check throttle interval
    
    # Connection Pool Constants
    DEFAULT_CONNECTION_POOL_SIZE = 5  # OSS connection pool size
    
    # Performance Thresholds
    CPU_WARNING_THRESHOLD = 80.0  # CPU usage warning threshold (%)
    CPU_CRITICAL_THRESHOLD = 95.0  # CPU usage critical threshold (%)
    MEMORY_WARNING_THRESHOLD = 75.0  # Memory usage warning threshold (%)
    MEMORY_CRITICAL_THRESHOLD = 90.0  # Memory usage critical threshold (%)
    THREAD_WARNING_THRESHOLD = 1000  # Thread count warning threshold
    THREAD_CRITICAL_THRESHOLD = 1500  # Thread count critical threshold

    # Emergency Resource Management
    EMERGENCY_THREAD_THRESHOLD = 2000  # Emergency thread cleanup threshold
    EMERGENCY_MEMORY_THRESHOLD = 95.0  # Emergency memory cleanup threshold
    FORCE_CLEANUP_INTERVAL_SECONDS = 60  # Force cleanup every minute under pressure


class FileConstants:
    """File and path related constants."""
    
    # File Extensions
    ARCHIVE_EXTENSION = ".tar.gz"
    JSON_EXTENSION = ".json"
    LOG_EXTENSION = ".log"
    
    # Directory Names
    DATA_DIR = "data"
    LOGS_DIR = "logs"
    TEMP_DIR = "temp"
    EXPORTS_DIR = "exports"
    CHECKPOINTS_DIR = "checkpoints"
    CONFIG_DIR = "config"
    
    # File Naming Patterns
    BACKUP_ID_FORMAT = "backup_{timestamp}"
    LOG_FILE_FORMAT = "tngd_backup_{date}.log"
    ARCHIVE_NAME_FORMAT = "{table}_{date}.tar.gz"


class ConfigConstants:
    """Configuration related constants."""
    
    # Default Configuration Files
    DEFAULT_CONFIG_FILE = "config/default.json"
    PRODUCTION_CONFIG_FILE = "config/production.json"
    DEVELOPMENT_CONFIG_FILE = "config/development.json"
    LARGE_DATASET_CONFIG_FILE = "config/large_dataset.json"
    TABLES_CONFIG_FILE = "config/tables.json"
    
    # Environment Variables
    DEVO_API_KEY_ENV = "DEVO_API_KEY"
    DEVO_API_SECRET_ENV = "DEVO_API_SECRET"
    OSS_ACCESS_KEY_ID_ENV = "OSS_ACCESS_KEY_ID"
    OSS_ACCESS_KEY_SECRET_ENV = "OSS_ACCESS_KEY_SECRET"
    OSS_ENDPOINT_ENV = "OSS_ENDPOINT"
    OSS_BUCKET_NAME_ENV = "OSS_BUCKET_NAME"


class ErrorConstants:
    """Error handling and retry constants."""
    
    # Retry Strategy
    EXPONENTIAL_BACKOFF_BASE = 2  # Base for exponential backoff
    MAX_BACKOFF_SECONDS = 300  # Maximum backoff time (5 minutes)
    
    # Error Categories
    RETRYABLE_ERRORS = [
        "ConnectionError",
        "TimeoutError", 
        "TemporaryFailure",
        "ServiceUnavailable"
    ]
    
    NON_RETRYABLE_ERRORS = [
        "AuthenticationError",
        "PermissionError",
        "InvalidConfiguration",
        "FileNotFound"
    ]


class SystemConstants:
    """System and version constants."""
    
    # Version Information
    VERSION = "2.0.0"
    SYSTEM_NAME = "TNGD Backup System"
    
    # Logging Configuration
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # Date and Time Formats
    DATE_FORMAT_INPUT = "%Y-%m-%d"
    TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
    ISO_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S"


# Convenience function to get all constants as a dictionary
def get_all_constants() -> Dict[str, Any]:
    """
    Get all constants as a nested dictionary for easy access.
    
    Returns:
        Dictionary containing all constant values organized by category
    """
    return {
        "backup": {
            attr: getattr(BackupConstants, attr)
            for attr in dir(BackupConstants)
            if not attr.startswith('_')
        },
        "file": {
            attr: getattr(FileConstants, attr)
            for attr in dir(FileConstants)
            if not attr.startswith('_')
        },
        "config": {
            attr: getattr(ConfigConstants, attr)
            for attr in dir(ConfigConstants)
            if not attr.startswith('_')
        },
        "error": {
            attr: getattr(ErrorConstants, attr)
            for attr in dir(ErrorConstants)
            if not attr.startswith('_')
        },
        "system": {
            attr: getattr(SystemConstants, attr)
            for attr in dir(SystemConstants)
            if not attr.startswith('_')
        }
    }
