{"version": "2.0", "description": "TNGD Backup System - Default Configuration", "resource_management": {"max_threads": 4, "memory_threshold_mb": 3000, "cpu_threshold_percent": 85, "disk_threshold_percent": 85, "cleanup_interval_seconds": 120, "resource_check_interval_seconds": 60, "thread_pool_timeout_seconds": 300, "force_cleanup_threshold": 1000}, "query_settings": {"default_timeout_seconds": 1800, "large_table_timeout_seconds": 3600, "max_retries": 3, "retry_delay_seconds": 60, "connection_timeout_seconds": 30, "read_timeout_seconds": 300, "query_heartbeat_interval": 30, "enable_query_cancellation": true}, "streaming_config": {"enabled": true, "default_chunk_size": 50000, "max_chunk_size": 100000, "min_chunk_size": 5000, "streaming_threshold_rows": 50000, "memory_threshold_mb": 1200, "progress_report_interval": 10, "memory_check_interval": 5, "enable_adaptive_chunking": true, "chunk_size_adjustment_factor": 0.5, "temp_file_cleanup": true, "aggressive_memory_management": true, "gc_interval": 3}, "storage_settings": {"upload_timeout_seconds": 1800, "max_upload_retries": 3, "retry_delay_seconds": 45, "chunk_size_mb": 25, "memory_threshold_percent": 70, "connection_pool_size": 2, "verify_integrity": true, "compress_before_upload": true, "parallel_uploads": false, "upload_queue_size": 5, "upload_path_structure": {"base_path": "", "provider_path": "Devo", "use_month_folders": true, "use_week_folders": true, "use_date_folders": true, "include_table_folders": false}}, "monitoring": {"enabled": true, "log_level": "INFO", "metrics_collection": true, "health_check_interval_seconds": 120, "thread_metrics_interval_seconds": 60, "suppress_thread_spam": true, "alert_thresholds": {"cpu_warning": 85, "cpu_critical": 95, "memory_warning": 80, "memory_critical": 90, "thread_warning": 1000, "thread_critical": 1500}, "emergency_response": {"enabled": true, "thread_emergency_threshold": 2000, "memory_emergency_threshold": 95, "cpu_emergency_threshold": 98, "force_cleanup_interval_seconds": 60}}, "large_tables": ["cef0.zscaler.nssweblog", "cloud.alibaba.log_service.events", "cloud.office365.management.exchange", "cloud.office365.management.securitycompliancecenter", "cloud.office365.management.endpoint", "cloud.office365.management.azureactivedirectory", "firewall.fortinet.traffic.forward", "my.app.tngd.polardb", "netstat.zscaler.analyzer_zpa"], "table_specific_settings": {"cef0.zscaler.nssweblog": {"chunk_size": 10000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 500}, "cloud.alibaba.log_service.events": {"chunk_size": 15000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 600}, "firewall.fortinet.traffic.forward": {"chunk_size": 15000, "timeout_seconds": 2400, "max_retries": 3, "memory_limit_mb": 600}, "cloud.office365.management.endpoint": {"chunk_size": 8000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 400}, "cloud.office365.management.securitycompliancecenter": {"chunk_size": 5000, "timeout_seconds": 3600, "max_retries": 4, "memory_limit_mb": 300, "enable_streaming": true, "aggressive_chunking": true}, "cloud.office365.management.azureactivedirectory": {"chunk_size": 8000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 400}}, "error_handling": {"max_consecutive_failures": 3, "failure_cooldown_minutes": 5, "auto_skip_problematic_tables": true, "detailed_error_logging": true, "error_notification_threshold": 3}, "recovery": {"checkpoint_enabled": true, "checkpoint_interval_minutes": 15, "auto_resume": true, "max_resume_attempts": 3, "resume_delay_minutes": 5}, "performance_optimizations": {"connection_pooling": true, "query_caching": false, "parallel_uploads": true, "memory_mapping": false, "compression_level": 6, "buffer_size_kb": 64}, "logging": {"level": "INFO", "max_file_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "separate_error_log": true, "log_rotation": true}}