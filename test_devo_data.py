#!/usr/bin/env python3
"""
Test script to verify data availability in Devo for specific tables and dates.
This script helps diagnose why backup is getting low row counts.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from tngd_backup.core.devo_client import DevoClient
from tngd_backup.core.config_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_table_data(table_name, test_dates):
    """Test data availability for a specific table across multiple dates."""
    print(f"\n🔍 Testing table: {table_name}")
    print("=" * 60)
    
    try:
        # Initialize Devo client directly
        devo_client = DevoClient()
        
        results = []
        
        for date_str in test_dates:
            print(f"\n📅 Testing date: {date_str}")
            
            # Test with simple count query first
            try:
                count_query = f"from {table_name} where eventdate = '{date_str}' select count(*) as total_count"
                count_result = devo_client.execute_query(count_query, days=1, timeout=300)
                
                if count_result:
                    total_count = count_result[0].get('total_count', 0)
                    print(f"   Count query result: {total_count:,} rows")
                else:
                    total_count = 0
                    print(f"   Count query result: No data returned")
                
            except Exception as e:
                print(f"   Count query failed: {str(e)}")
                total_count = "ERROR"
            
            # Test with sample data query
            try:
                sample_query = f"from {table_name} where eventdate = '{date_str}' select * limit 10"
                sample_result = devo_client.execute_query(sample_query, days=1, timeout=300)
                
                sample_count = len(sample_result) if sample_result else 0
                print(f"   Sample query result: {sample_count} rows (limit 10)")
                
                if sample_result and len(sample_result) > 0:
                    print(f"   Sample data keys: {list(sample_result[0].keys())}")
                
            except Exception as e:
                print(f"   Sample query failed: {str(e)}")
                sample_count = "ERROR"
            
            # Test with timestamp-based query (like backup system uses)
            try:
                # Convert date to timestamp range
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                start_timestamp = int(date_obj.timestamp() * 1000)
                end_timestamp = int((date_obj + timedelta(days=1) - timedelta(seconds=1)).timestamp() * 1000)
                
                timestamp_query = f"from {table_name} where eventdate >= {start_timestamp} and eventdate <= {end_timestamp} select count(*) as total_count"
                timestamp_result = devo_client.execute_query(timestamp_query, days=1, timeout=300)
                
                if timestamp_result:
                    timestamp_count = timestamp_result[0].get('total_count', 0)
                    print(f"   Timestamp query result: {timestamp_count:,} rows")
                else:
                    timestamp_count = 0
                    print(f"   Timestamp query result: No data returned")
                
            except Exception as e:
                print(f"   Timestamp query failed: {str(e)}")
                timestamp_count = "ERROR"
            
            results.append({
                'date': date_str,
                'table': table_name,
                'count_query': total_count,
                'sample_query': sample_count,
                'timestamp_query': timestamp_count
            })
        
        return results
        
    except Exception as e:
        print(f"❌ Error testing table {table_name}: {str(e)}")
        return []

def main():
    """Main test function."""
    print("🧪 DEVO DATA AVAILABILITY TEST")
    print("=" * 60)
    
    # Test dates - including the problematic date and some recent dates
    test_dates = [
        '2025-04-14',  # The date from your backup report
        '2025-07-01',  # Recent date
        '2025-07-02',  # Recent date
        '2025-07-13',  # Today
    ]
    
    # Test tables - focus on ones that should have more data
    test_tables = [
        'cloud.alibaba.log_service.events',  # Showed 53 rows
        'cef0.zscaler.nssweblog',           # Showed 1 row
        'my.app.tngd.actiontraillinux',     # Showed 4 rows
        'my.app.tngd.discoswitch',          # Showed 12 rows
    ]
    
    all_results = []
    
    for table in test_tables:
        results = test_table_data(table, test_dates)
        all_results.extend(results)
    
    # Save results to file
    results_file = f"devo_data_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n📊 Results saved to: {results_file}")
    
    # Summary
    print(f"\n📋 SUMMARY")
    print("=" * 60)
    
    for result in all_results:
        if result['count_query'] != 'ERROR' and result['count_query'] != 0:
            print(f"✅ {result['table']} on {result['date']}: {result['count_query']:,} rows available")
        elif result['count_query'] == 0:
            print(f"⚠️  {result['table']} on {result['date']}: No data found")
        else:
            print(f"❌ {result['table']} on {result['date']}: Query failed")

if __name__ == "__main__":
    main()
