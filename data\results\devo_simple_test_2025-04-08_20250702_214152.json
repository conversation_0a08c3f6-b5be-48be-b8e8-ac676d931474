{"test_date": "2025-04-08", "timestamp": "20250702_214152", "results": {"my.app.tngd.h3ccoreswitch": {"success": true, "row_count": 2, "data": [{"eventdate": 1744099733819, "cluster": "-", "instance": "-", "message": "TNG-Controller-2 %%10DPIM/4/ANTIVIRUS_WARNING: The anti-virus feature has no available license."}, {"eventdate": 1744099822852, "cluster": "-", "instance": "-", "message": "Host: *************:13010"}]}, "my.app.tngd.keeper": {"success": true, "row_count": 12, "data": [{"eventdate": 1744099822933, "cluster": "-", "instance": "-", "message": "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)"}, {"eventdate": 1744099822933, "cluster": "-", "instance": "-", "message": "Accept-Encoding: gzip"}]}, "my.app.tngd.h3cswitch": {"success": true, "row_count": 10, "data": [{"eventdate": 1744074004929, "cluster": "-", "instance": "-", "message": "ACS-TNGD-L10-1 %%10SHELL/6/SHELL_CMD: -Line=-IPAddr=**-User=**; Command is system-view"}, {"eventdate": 1744074005845, "cluster": "-", "instance": "-", "message": "ACS-TNGD-L10-1 %%10SHELL/6/SHELL_CMD: -Line=-IPAddr=**-User=**; Command is interface range GigabitEthernet 2/0/17"}]}, "my.app.tngd.waf": {"success": true, "row_count": 158, "data": [{"eventdate": 1744070400241, "cluster": "-", "instance": "-", "messages": "{\"time\":\"2025-03-04T05:43:12+08:00\",\"ssl_protocol\":\"TLSv1.3\",\"http_x_forwarded_for\":\"-\",\"body_bytes_sent\":\"0\",\"request_time_msec\":\"0\",\"request_method\":\"POST\",\"dst_port\":\"443\",\"upstream_response_time\":\"-\",\"https\":\"on\",\"ssl_cipher\":\"TLS_AES_256_GCM_SHA384\",\"host\":\"mpaasgw.tngdigital.com.my\",\"real_client_ip\":\"**************\",\"request_traceid\":\"ac11000117410381921903244e0096\",\"src_ip\":\"**************\",\"region\":\"int\",\"request_uri\":\"/mgw.htm\",\"src_port\":\"36870\",\"status\":\"499\",\"matched_host\":\"mpaasgw.tngdigital.com.my-waf\",\"http_referer\":\"https://m.tngdigital.com.my/\",\"remote_addr\":\"**************\",\"http_cookie\":\"-\",\"__topic__\":\"waf_access_log\",\"request_path\":\"/mgw.htm\",\"querystring\":\"-\",\"__source__\":\"xlogc\",\"remote_port\":\"36870\",\"start_time\":\"1741038192\",\"user_id\":\"5478802082436037\",\"http_user_agent\":\"Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36\",\"server_protocol\":\"HTTP/2.0\",\"content_type\":\"application/x-www-form-urlencoded; charset=UTF-8\",\"request_length\":\"1186\",\"upstream_status\":\"-\",\"__time__\":\"1741038192\",\"upstream_addr\":\"-\"}", "messages___time__": "1741038192", "messages___topic__": "waf_access_log", "messages___source__": "xlogc", "messages_http_referer": "https://m.tngdigital.com.my/", "messages_http_cookie": "-", "messages_ssl_protocol": "TLSv1.3", "messages_src_ip": 3541681143, "messages_server_protocol": "HTTP/2.0", "messages_src_port": "36870", "messages_remote_port": "36870", "messages_remote_addr": 3541681143, "messages_http_x_forwarded_for": "-", "messages_start_time": "1741038192", "messages_upstream_status": "-", "messages_region": "int", "messages_upstream_response_time": "-", "messages_querystring": "-", "messages_body_bytes_sent": "0", "messages_request_method": "POST", "messages_request_uri": "/mgw.htm", "messages_real_client_ip": 3541681143, "messages_matched_host": "mpaasgw.tngdigital.com.my-waf", "messages_upstream_addr": "-", "messages_content_type": "application/x-www-form-urlencoded; charset=UTF-8", "messages_dst_port": "443", "messages_status": "499", "messages_request_traceid": "ac11000117410381921903244e0096", "messages_request_length": "1186", "messages_time": "2025-03-04T05:43:12+08:00", "messages_https": "on", "messages_request_path": "/mgw.htm", "messages_request_time_msec": "0", "messages_host": "mpaasgw.tngdigital.com.my", "messages_http_user_agent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36", "messages_user_id": "5478802082436037", "messages_ssl_cipher": "TLS_AES_256_GCM_SHA384", "messages_final_plugin": null, "messages_final_rule_id": null, "messages_final_action": null, "messages_bypass_matched_ids": null, "messages_request_traceid_origin": null}, {"eventdate": 1744070400253, "cluster": "-", "instance": "-", "messages": "{\"time\":\"2025-03-04T05:43:15+08:00\",\"ssl_protocol\":\"TLSv1.2\",\"http_x_forwarded_for\":\"-\",\"body_bytes_sent\":\"2657\",\"request_time_msec\":\"2\",\"request_method\":\"POST\",\"final_action\":\"block\",\"final_rule_id\":\"20081570\",\"dst_port\":\"443\",\"upstream_response_time\":\"-\",\"https\":\"on\",\"host\":\"remit-callback.tngdigital.com.my\",\"ssl_cipher\":\"ECDHE-RSA-AES128-GCM-SHA256\",\"real_client_ip\":\"*************\",\"request_traceid\":\"ac11000117410381950912236e0066\",\"src_ip\":\"*************\",\"region\":\"int\",\"request_uri\":\"/MiniPorgram/CAllBack\",\"src_port\":\"57250\",\"status\":\"405\",\"matched_host\":\"remit-callback.tngdigital.com.my-waf\",\"http_referer\":\"-\",\"remote_addr\":\"*************\",\"http_cookie\":\"$Version=0; acw_tc=ac11000117410366900883568e00713307b616a108c2a365a068889e801b41; $Path=/\",\"__topic__\":\"waf_access_log\",\"request_path\":\"/MiniPorgram/CAllBack\",\"querystring\":\"-\",\"__source__\":\"xlogc\",\"remote_port\":\"57250\",\"start_time\":\"1741038195\",\"http_user_agent\":\"Jakarta Commons-HttpClient/3.1\",\"final_plugin\":\"acl\",\"user_id\":\"5478802082436037\",\"server_protocol\":\"HTTP/1.1\",\"content_type\":\"application/json; charset=UTF-8\",\"request_length\":\"1114\",\"upstream_status\":\"-\",\"__time__\":\"1741038195\",\"upstream_addr\":\"-\"}", "messages___time__": "1741038195", "messages___topic__": "waf_access_log", "messages___source__": "xlogc", "messages_http_referer": "-", "messages_http_cookie": "$Version=0; acw_tc=ac11000117410366900883568e00713307b616a108c2a365a068889e801b41; $Path=/", "messages_ssl_protocol": "TLSv1.2", "messages_src_ip": 805230102, "messages_server_protocol": "HTTP/1.1", "messages_src_port": "57250", "messages_remote_port": "57250", "messages_remote_addr": 805230102, "messages_http_x_forwarded_for": "-", "messages_start_time": "1741038195", "messages_upstream_status": "-", "messages_region": "int", "messages_upstream_response_time": "-", "messages_querystring": "-", "messages_body_bytes_sent": "2657", "messages_request_method": "POST", "messages_request_uri": "/MiniPorgram/CAllBack", "messages_real_client_ip": 805230102, "messages_matched_host": "remit-callback.tngdigital.com.my-waf", "messages_upstream_addr": "-", "messages_content_type": "application/json; charset=UTF-8", "messages_dst_port": "443", "messages_status": "405", "messages_request_traceid": "ac11000117410381950912236e0066", "messages_request_length": "1114", "messages_time": "2025-03-04T05:43:15+08:00", "messages_https": "on", "messages_request_path": "/MiniPorgram/CAllBack", "messages_request_time_msec": "2", "messages_host": "remit-callback.tngdigital.com.my", "messages_http_user_agent": "Jakarta Commons-HttpClient/3.1", "messages_user_id": "5478802082436037", "messages_ssl_cipher": "ECDHE-RSA-AES128-GCM-SHA256", "messages_final_plugin": "acl", "messages_final_rule_id": "20081570", "messages_final_action": "block", "messages_bypass_matched_ids": null, "messages_request_traceid_origin": null}]}, "my.app.tngd.actiontraillinux": {"success": true, "row_count": 576, "data": [{"eventdate": 1744070402869, "cluster": "-", "instance": "-", "messages": "{\"__tag__:__path__\":\"/var/log/secure\",\"__tag__:__pack_id__\":\"F6F66EE1246D923-2B32\",\"content\":\"2025-04-08T08:00:09+08:00 ************* iZ8ps47wx4fe2e953ohps7Z sshd[23721]: Did not receive identification string from ************* port 35798\",\"__topic__\":\"\",\"__tag__:__hostname__\":\"security-syslog-1-3\",\"__time__\":\"1744070399\",\"__source__\":\"************\"}", "messages___time__": "1744070399", "messages___topic__": "", "messages___source__": 184290841, "messages___tag__:__hostname__": "security-syslog-1-3", "messages___tag__:__path__": "/var/log/secure", "messages___tag__:__pack_id__": "F6F66EE1246D923-2B32", "messages_content": "2025-04-08T08:00:09+08:00 ************* iZ8ps47wx4fe2e953ohps7Z sshd[23721]: Did not receive identification string from ************* port 35798"}, {"eventdate": 1744070405868, "cluster": "-", "instance": "-", "messages": "{\"__tag__:__path__\":\"/var/log/secure\",\"__tag__:__pack_id__\":\"F6F66EE1246D923-2B33\",\"content\":\"2025-04-08T08:00:00+08:00 ************ prod-apsettlement-A-8 sshd[3264782]: error: kex_exchange_identification: Connection closed by remote host\",\"__topic__\":\"\",\"__tag__:__hostname__\":\"security-syslog-1-3\",\"__time__\":\"1744070400\",\"__source__\":\"************\"}", "messages___time__": "1744070400", "messages___topic__": "", "messages___source__": 184290841, "messages___tag__:__hostname__": "security-syslog-1-3", "messages___tag__:__path__": "/var/log/secure", "messages___tag__:__pack_id__": "F6F66EE1246D923-2B33", "messages_content": "2025-04-08T08:00:00+08:00 ************ prod-apsettlement-A-8 sshd[3264782]: error: kex_exchange_identification: Connection closed by remote host"}]}}}