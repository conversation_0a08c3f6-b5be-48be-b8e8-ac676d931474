#!/usr/bin/env python3
"""
Resource Monitor Dashboard

Real-time monitoring dashboard for TNGD backup system resources.
Shows CPU, memory, thread count, and other critical metrics.
"""

import os
import sys
import time
import threading
import psutil
import json
from datetime import datetime
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class ResourceDashboard:
    def __init__(self):
        self.running = False
        self.history = []
        self.max_history = 100
        
    def get_metrics(self):
        """Get current system metrics."""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            # Thread metrics
            thread_count = threading.active_count()
            
            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            process_cpu = process.cpu_percent()
            
            # Network connections (if available)
            try:
                connections = len(process.connections())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                connections = 0
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024 / 1024 / 1024,
                'memory_used_gb': memory.used / 1024 / 1024 / 1024,
                'disk_percent': disk.percent,
                'disk_free_gb': disk.free / 1024 / 1024 / 1024,
                'thread_count': thread_count,
                'process_memory_mb': process_memory,
                'process_cpu_percent': process_cpu,
                'network_connections': connections
            }
        except Exception as e:
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def get_thread_details(self):
        """Get detailed thread information."""
        try:
            all_threads = threading.enumerate()
            thread_info = {}
            
            for thread in all_threads:
                name_prefix = thread.name.split('-')[0] if '-' in thread.name else thread.name
                if name_prefix not in thread_info:
                    thread_info[name_prefix] = {
                        'count': 0,
                        'daemon': 0,
                        'alive': 0
                    }
                
                thread_info[name_prefix]['count'] += 1
                if thread.daemon:
                    thread_info[name_prefix]['daemon'] += 1
                if thread.is_alive():
                    thread_info[name_prefix]['alive'] += 1
            
            return thread_info
        except Exception as e:
            return {'error': str(e)}
    
    def check_backup_status(self):
        """Check if backup is currently running."""
        try:
            # Check for backup log files
            log_dir = Path("data/logs")
            if log_dir.exists():
                log_files = list(log_dir.glob("tngd_backup_*.log"))
                if log_files:
                    # Get the most recent log file
                    latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
                    
                    # Check if it was modified recently (within last 5 minutes)
                    last_modified = datetime.fromtimestamp(latest_log.stat().st_mtime)
                    time_diff = datetime.now() - last_modified
                    
                    if time_diff.total_seconds() < 300:  # 5 minutes
                        return {
                            'status': 'running',
                            'log_file': str(latest_log),
                            'last_activity': last_modified.isoformat()
                        }
            
            return {'status': 'idle'}
        except Exception as e:
            return {'status': 'unknown', 'error': str(e)}
    
    def display_dashboard(self):
        """Display the resource dashboard."""
        while self.running:
            try:
                # Clear screen
                os.system('cls' if os.name == 'nt' else 'clear')
                
                # Get current metrics
                metrics = self.get_metrics()
                thread_details = self.get_thread_details()
                backup_status = self.check_backup_status()
                
                # Add to history
                if 'error' not in metrics:
                    self.history.append(metrics)
                    if len(self.history) > self.max_history:
                        self.history = self.history[-self.max_history:]
                
                # Display header
                print("=" * 80)
                print("TNGD BACKUP SYSTEM - RESOURCE MONITOR DASHBOARD")
                print(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)
                
                # Display current metrics
                if 'error' in metrics:
                    print(f"ERROR: {metrics['error']}")
                else:
                    print(f"CPU Usage:      {metrics['cpu_percent']:6.1f}%")
                    print(f"Memory Usage:   {metrics['memory_percent']:6.1f}% ({metrics['memory_used_gb']:.1f}GB used, {metrics['memory_available_gb']:.1f}GB free)")
                    print(f"Disk Usage:     {metrics['disk_percent']:6.1f}% ({metrics['disk_free_gb']:.1f}GB free)")
                    print(f"Thread Count:   {metrics['thread_count']:6d}")
                    print(f"Process Memory: {metrics['process_memory_mb']:6.1f}MB")
                    print(f"Process CPU:    {metrics['process_cpu_percent']:6.1f}%")
                    print(f"Network Conn:   {metrics['network_connections']:6d}")
                
                print("-" * 80)
                
                # Display backup status
                print(f"Backup Status: {backup_status['status'].upper()}")
                if backup_status['status'] == 'running':
                    print(f"Log File: {backup_status['log_file']}")
                    print(f"Last Activity: {backup_status['last_activity']}")
                
                print("-" * 80)
                
                # Display thread details
                print("THREAD BREAKDOWN:")
                if 'error' in thread_details:
                    print(f"ERROR: {thread_details['error']}")
                else:
                    for name, info in sorted(thread_details.items()):
                        print(f"  {name:20s}: {info['count']:4d} total, {info['daemon']:4d} daemon, {info['alive']:4d} alive")
                
                print("-" * 80)
                
                # Display alerts
                alerts = []
                if 'error' not in metrics:
                    if metrics['cpu_percent'] > 90:
                        alerts.append(f"HIGH CPU: {metrics['cpu_percent']:.1f}%")
                    if metrics['memory_percent'] > 85:
                        alerts.append(f"HIGH MEMORY: {metrics['memory_percent']:.1f}%")
                    if metrics['thread_count'] > 1500:
                        alerts.append(f"HIGH THREAD COUNT: {metrics['thread_count']}")
                    if metrics['disk_percent'] > 90:
                        alerts.append(f"LOW DISK SPACE: {metrics['disk_percent']:.1f}%")
                
                if alerts:
                    print("ALERTS:")
                    for alert in alerts:
                        print(f"  ⚠️  {alert}")
                else:
                    print("✅ No alerts - system appears healthy")
                
                print("-" * 80)
                print("Press Ctrl+C to exit")
                
                # Wait before next update
                time.sleep(5)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Dashboard error: {e}")
                time.sleep(5)
    
    def start(self):
        """Start the dashboard."""
        self.running = True
        try:
            self.display_dashboard()
        finally:
            self.running = False
    
    def save_metrics_to_file(self, filename="resource_metrics.json"):
        """Save collected metrics to a file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.history, f, indent=2)
            print(f"Metrics saved to {filename}")
        except Exception as e:
            print(f"Failed to save metrics: {e}")

def main():
    """Main function."""
    dashboard = ResourceDashboard()
    
    try:
        print("Starting TNGD Resource Monitor Dashboard...")
        print("Press Ctrl+C to exit and save metrics")
        dashboard.start()
    except KeyboardInterrupt:
        print("\nShutting down dashboard...")
    finally:
        # Save metrics before exit
        dashboard.save_metrics_to_file()
        print("Dashboard stopped.")

if __name__ == "__main__":
    main()
