{"timestamp": "2025-07-02T20:09:07.347081", "target_date": "2025-06-25", "table_analyses": {"my.app.tngd.polardb": {"eventdate_analysis": {"table_name": "my.app.tngd.polardb", "sample_eventdates": [{"row_index": 0, "value": 1750809600123, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 1, "value": 1750809600129, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 2, "value": 1750809600131, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 3, "value": 1750809600132, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 4, "value": 1750809600134, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}], "eventdate_types": {"int": 5}, "date_ranges_found": ["2025-06-25"], "issues": []}, "filtering_analysis": {"table_name": "my.app.tngd.polardb", "target_date": "2025-06-25", "filtering_tests": {"string_equality": {"query": "from my.app.tngd.polardb where eventdate = '2025-06-25' select * limit 50", "row_count": 1, "sample_eventdates": [1750809600123]}, "timestamp_range": {"query": "from my.app.tngd.polardb where eventdate >= 1750780800000 and eventdate <= 1750867199000 select * limit 50", "row_count": 2, "timestamp_range": "1750780800000 to 1750867199000", "sample_eventdates": [1750809600123, 1750809600129]}, "string_contains": {"query": "from my.app.tngd.polardb where eventdate like '%2025-06-25%' select * limit 50", "row_count": 1, "sample_eventdates": [1751328000107]}, "date_function": {"query": "from my.app.tngd.polardb where toDate(eventdate) = '2025-06-25' select * limit 50", "row_count": 2, "sample_eventdates": [1751328000107, 1751328000117]}, "broad_timestamp_range": {"query": "from my.app.tngd.polardb where eventdate >= 1750737600000 and eventdate <= 1750910400000 select * limit 50", "row_count": 1, "timestamp_range": "1750737600000 to 1750910400000", "sample_eventdates": [1750723200029]}}, "issues": []}}, "cloud.office365.management.exchange": {"eventdate_analysis": {"table_name": "cloud.office365.management.exchange", "sample_eventdates": [{"row_index": 0, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 1, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 2, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 3, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 4, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 5, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 6, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 7, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 8, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 9, "value": 1750809645866, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 10, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 11, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 12, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 13, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 14, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 15, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 16, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 17, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 18, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}, {"row_index": 19, "value": 1750809645867, "type": "int", "converted_date": "2025-06-25 08:00:45", "is_timestamp": true}], "eventdate_types": {"int": 20}, "date_ranges_found": ["2025-06-25"], "issues": []}, "filtering_analysis": {"table_name": "cloud.office365.management.exchange", "target_date": "2025-06-25", "filtering_tests": {"string_equality": {"query": "from cloud.office365.management.exchange where eventdate = '2025-06-25' select * limit 50", "row_count": 1, "sample_eventdates": [1750809645866]}, "timestamp_range": {"query": "from cloud.office365.management.exchange where eventdate >= 1750780800000 and eventdate <= 1750867199000 select * limit 50", "row_count": 3, "timestamp_range": "1750780800000 to 1750867199000", "sample_eventdates": [1750809645866, 1750809645866, 1750809645866]}, "string_contains": {"query": "from cloud.office365.management.exchange where eventdate like '%2025-06-25%' select * limit 50", "row_count": 1, "sample_eventdates": [1751328048963]}, "date_function": {"query": "from cloud.office365.management.exchange where toDate(eventdate) = '2025-06-25' select * limit 50", "row_count": 11, "sample_eventdates": [1751328048963, 1751328048964, 1751328048964, 1751328048964, 1751328048964]}, "broad_timestamp_range": {"query": "from cloud.office365.management.exchange where eventdate >= 1750737600000 and eventdate <= 1750910400000 select * limit 50", "row_count": 59, "timestamp_range": "1750737600000 to 1750910400000", "sample_eventdates": [1750723270367, 1750723270367, 1750723270367, 1750723270367, 1750723270367]}}, "issues": []}}, "firewall.fortinet.traffic.forward": {"eventdate_analysis": {"table_name": "firewall.fortinet.traffic.forward", "sample_eventdates": [{"row_index": 0, "value": 1750809600162, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 1, "value": 1750809600187, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 2, "value": 1750809600187, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 3, "value": 1750809600188, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 4, "value": 1750809600189, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 5, "value": 1750809600189, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 6, "value": 1750809600190, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 7, "value": 1750809600194, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 8, "value": 1750809600196, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 9, "value": 1750809600197, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 10, "value": 1750809600198, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 11, "value": 1750809600199, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 12, "value": 1750809600201, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 13, "value": 1750809600202, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 14, "value": 1750809600204, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 15, "value": 1750809600206, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 16, "value": 1750809600207, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 17, "value": 1750809600208, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 18, "value": 1750809600209, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}, {"row_index": 19, "value": 1750809600213, "type": "int", "converted_date": "2025-06-25 08:00:00", "is_timestamp": true}], "eventdate_types": {"int": 20}, "date_ranges_found": ["2025-06-25"], "issues": []}, "filtering_analysis": {"table_name": "firewall.fortinet.traffic.forward", "target_date": "2025-06-25", "filtering_tests": {"string_equality": {"query": "from firewall.fortinet.traffic.forward where eventdate = '2025-06-25' select * limit 50", "row_count": 2, "sample_eventdates": [1750809600162, 1750809600187]}, "timestamp_range": {"query": "from firewall.fortinet.traffic.forward where eventdate >= 1750780800000 and eventdate <= 1750867199000 select * limit 50", "row_count": 1, "timestamp_range": "1750780800000 to 1750867199000", "sample_eventdates": [1750809600162]}, "string_contains": {"query": "from firewall.fortinet.traffic.forward where eventdate like '%2025-06-25%' select * limit 50", "row_count": 1, "sample_eventdates": [1751328000008]}, "date_function": {"query": "from firewall.fortinet.traffic.forward where toDate(eventdate) = '2025-06-25' select * limit 50", "row_count": 1, "sample_eventdates": [1751328000008]}, "broad_timestamp_range": {"query": "from firewall.fortinet.traffic.forward where eventdate >= 1750737600000 and eventdate <= 1750910400000 select * limit 50", "row_count": 4, "timestamp_range": "1750737600000 to 1750910400000", "sample_eventdates": [1750723200024, 1750723200027, 1750723200027, 1750723200040]}}, "issues": []}}}, "summary": {"tables_with_mixed_formats": 0, "tables_with_timestamp_only": 3, "tables_with_string_only": 0, "recommended_approach": "TIMESTAMP_ONLY: Use timestamp range filtering", "issues_found": []}}